#!/bin/bash

# Firecrawl 服务测试脚本 (使用 curl)
# 使用方法: ./scripts/test-firecrawl-curl.sh [url]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
FIRECRAWL_BASE_URL=${FIRECRAWL_BASE_URL:-"http://localhost:3002"}
TEST_URL=${1:-"https://example.com"}

echo -e "${BLUE}🔥 Firecrawl 服务测试${NC}"
echo "=================================="
echo -e "服务地址: ${YELLOW}${FIRECRAWL_BASE_URL}${NC}"
echo -e "测试 URL: ${YELLOW}${TEST_URL}${NC}"
echo "=================================="

# 检测是否为本地服务
if [[ "$FIRECRAWL_BASE_URL" == *"localhost"* ]] || [[ "$FIRECRAWL_BASE_URL" == *"127.0.0.1"* ]]; then
    API_VERSION="v0"
    echo -e "${BLUE}检测到本地 Firecrawl 服务，使用 API 版本: ${API_VERSION}${NC}"
else
    API_VERSION="v1"
    echo -e "${BLUE}检测到云端 Firecrawl 服务，使用 API 版本: ${API_VERSION}${NC}"
fi

ENDPOINT="${FIRECRAWL_BASE_URL}/${API_VERSION}/scrape"
echo -e "请求端点: ${YELLOW}${ENDPOINT}${NC}"
echo ""

# 测试基本连接
echo -e "${BLUE}📡 测试基本连接...${NC}"
if curl -s --connect-timeout 5 "${FIRECRAWL_BASE_URL}/" > /dev/null; then
    echo -e "${GREEN}✅ 基本连接成功${NC}"
else
    echo -e "${RED}❌ 无法连接到 Firecrawl 服务${NC}"
    echo -e "${YELLOW}请检查：${NC}"
    echo "1. Firecrawl 服务是否在运行"
    echo "2. 服务地址是否正确: ${FIRECRAWL_BASE_URL}"
    echo "3. 网络连接是否正常"
    exit 1
fi

# 测试抓取功能
echo -e "${BLUE}🕷️  测试网页抓取功能...${NC}"

# 创建请求数据
REQUEST_DATA=$(cat <<EOF
{
  "url": "${TEST_URL}",
  "formats": ["markdown"],
  "waitFor": 1000,
  "timeout": 10000,
  "blockAds": true
}
EOF
)

# 发送请求
echo -e "${YELLOW}发送请求到: ${ENDPOINT}${NC}"
RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dummy_key_for_local" \
  -d "${REQUEST_DATA}")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n 1)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '$d')

echo -e "HTTP 状态码: ${YELLOW}${HTTP_CODE}${NC}"

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}🎉 抓取成功！${NC}"
    
    # 解析响应
    if command -v jq &> /dev/null; then
        echo -e "${BLUE}📊 响应详情:${NC}"
        
        # 检查是否成功
        SUCCESS=$(echo "$RESPONSE_BODY" | jq -r '.success // false')
        if [ "$SUCCESS" = "true" ]; then
            # 提取信息
            TITLE=$(echo "$RESPONSE_BODY" | jq -r '.data.metadata.title // "未知"')
            CONTENT_LENGTH=$(echo "$RESPONSE_BODY" | jq -r '.data.markdown // "" | length')
            
            echo -e "📋 页面标题: ${GREEN}${TITLE}${NC}"
            echo -e "📄 内容长度: ${GREEN}${CONTENT_LENGTH}${NC} 字符"
            
            # 显示内容预览
            PREVIEW=$(echo "$RESPONSE_BODY" | jq -r '.data.markdown // ""' | head -c 200)
            if [ -n "$PREVIEW" ]; then
                echo -e "${BLUE}📖 内容预览:${NC}"
                echo "------------------------------"
                echo "$PREVIEW..."
                echo "------------------------------"
            fi
            
            echo -e "${GREEN}✅ 所有测试通过！Firecrawl 服务配置正确。${NC}"
            echo -e "${BLUE}现在可以启动 Open Lovable 项目了:${NC}"
            echo "npm run dev"
            
        else
            echo -e "${RED}❌ Firecrawl 返回失败结果${NC}"
            echo "$RESPONSE_BODY" | jq '.' 2>/dev/null || echo "$RESPONSE_BODY"
            exit 1
        fi
    else
        echo -e "${YELLOW}⚠️  未安装 jq，无法解析 JSON 响应${NC}"
        echo -e "${BLUE}原始响应:${NC}"
        echo "$RESPONSE_BODY"
        
        # 简单检查是否包含成功标识
        if echo "$RESPONSE_BODY" | grep -q '"success":true'; then
            echo -e "${GREEN}✅ 响应包含成功标识，测试通过！${NC}"
        else
            echo -e "${RED}❌ 响应不包含成功标识${NC}"
            exit 1
        fi
    fi
    
elif [ "$HTTP_CODE" = "404" ]; then
    echo -e "${RED}❌ 端点不存在 (404)${NC}"
    echo -e "${YELLOW}可能的原因:${NC}"
    echo "1. API 版本不正确 (当前使用: ${API_VERSION})"
    echo "2. Firecrawl 服务版本与预期不符"
    echo "3. 服务路由配置问题"
    echo ""
    echo -e "${BLUE}响应内容:${NC}"
    echo "$RESPONSE_BODY"
    exit 1
    
else
    echo -e "${RED}❌ 请求失败 (HTTP ${HTTP_CODE})${NC}"
    echo -e "${BLUE}响应内容:${NC}"
    echo "$RESPONSE_BODY"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
