#!/usr/bin/env node

/**
 * 简单的 Firecrawl 测试脚本
 */

import { HttpsProxyAgent } from 'https-proxy-agent';
import { HttpProxyAgent } from 'http-proxy-agent';

// 检查代理设置
const httpProxy = process.env.http_proxy || process.env.HTTP_PROXY;
const httpsProxy = process.env.https_proxy || process.env.HTTPS_PROXY;

const FIRECRAWL_BASE_URL = process.env.FIRECRAWL_BASE_URL || 'http://localhost:3002';
const testUrl = process.argv[2] || 'https://example.com';

console.log('🔥 简单 Firecrawl 测试');
console.log(`服务地址: ${FIRECRAWL_BASE_URL}`);
console.log(`测试 URL: ${testUrl}`);
console.log(`HTTP 代理: ${httpProxy || '无'}`);

async function test() {
  try {
    const isLocal = FIRECRAWL_BASE_URL.includes('localhost');
    const apiVersion = isLocal ? 'v0' : 'v1';
    const endpoint = `${FIRECRAWL_BASE_URL}/${apiVersion}/scrape`;

    console.log(`请求端点: ${endpoint}`);

    // 配置代理
    const fetchOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dummy_key'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown']
      })
    };

    // 如果有代理设置，添加代理 agent
    if (httpProxy && isLocal) {
      const agent = new HttpProxyAgent(httpProxy);
      fetchOptions.agent = agent;
    }

    const response = await fetch(endpoint, fetchOptions);
    
    console.log(`状态码: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 成功!');
      console.log(`标题: ${data.data?.metadata?.title || '未知'}`);
      console.log(`内容长度: ${data.data?.markdown?.length || 0} 字符`);
      
      if (data.data?.markdown) {
        console.log('\n内容预览:');
        console.log(data.data.markdown.substring(0, 200) + '...');
      }
    } else {
      const text = await response.text();
      console.log('❌ 失败');
      console.log('响应:', text);
    }
  } catch (error) {
    console.log('❌ 错误:', error.message);
  }
}

test();
