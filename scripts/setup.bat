@echo off
setlocal enabledelayedexpansion

REM Open Lovable 快速设置脚本 (Windows 版本)
REM 用于配置本地 Firecrawl 环境

echo 🚀 Open Lovable 快速设置脚本
echo ==================================

REM 检查 Node.js
echo 检查 Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do (
    set NODE_MAJOR=%%a
    set NODE_MAJOR=!NODE_MAJOR:v=!
)

if !NODE_MAJOR! LSS 18 (
    echo ❌ Node.js 版本过低，需要 18+
    pause
    exit /b 1
)

echo ✅ Node.js 版本: 
node --version

REM 检查包管理器
echo 检查包管理器...
pnpm --version >nul 2>&1
if not errorlevel 1 (
    set PKG_MANAGER=pnpm
    echo ✅ 使用 pnpm
) else (
    npm --version >nul 2>&1
    if not errorlevel 1 (
        set PKG_MANAGER=npm
        echo ✅ 使用 npm
    ) else (
        echo ❌ 未找到 npm 或 pnpm
        pause
        exit /b 1
    )
)

REM 安装依赖
echo 安装项目依赖...
if "!PKG_MANAGER!"=="pnpm" (
    pnpm install
) else (
    npm install
)
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

REM 创建环境变量文件
echo 配置环境变量...
if not exist ".env.local" (
    copy .env.example .env.local >nul
    echo ✅ 已创建 .env.local 文件
) else (
    echo ⚠️  .env.local 文件已存在，跳过创建
)

REM 配置本地 Firecrawl
findstr /C:"FIRECRAWL_BASE_URL=http://localhost:3002" .env.local >nul
if not errorlevel 1 (
    echo ✅ 本地 Firecrawl 配置已存在
) else (
    echo 📝 配置本地 Firecrawl...
    
    REM 创建临时文件来更新 .env.local
    (
        for /f "usebackq delims=" %%i in (".env.local") do (
            set "line=%%i"
            if "!line:FIRECRAWL_BASE_URL=!" neq "!line!" (
                echo FIRECRAWL_BASE_URL=http://localhost:3002
            ) else if "!line:FIRECRAWL_API_KEY=your_firecrawl_api_key_here!" neq "!line!" (
                echo FIRECRAWL_API_KEY=dummy_key_for_local
            ) else (
                echo !line!
            )
        )
        REM 如果没有找到 FIRECRAWL_BASE_URL，添加它
        findstr /C:"FIRECRAWL_BASE_URL=" .env.local >nul
        if errorlevel 1 (
            echo FIRECRAWL_BASE_URL=http://localhost:3002
        )
    ) > .env.local.tmp
    
    move .env.local.tmp .env.local >nul
    echo ✅ 本地 Firecrawl 配置完成
)

REM 测试 Firecrawl 连接
echo 测试 Firecrawl 连接...
if exist "scripts\test-firecrawl.js" (
    node scripts\test-firecrawl.js
    if not errorlevel 1 (
        echo ✅ Firecrawl 连接测试通过
        goto :show_success
    ) else (
        echo ❌ Firecrawl 连接测试失败
        goto :show_config_reminder
    )
) else (
    echo ❌ 测试脚本不存在
    goto :show_config_reminder
)

:show_success
echo.
echo 🎉 设置完成！
echo ==================================
echo 启动开发服务器：
if "!PKG_MANAGER!"=="pnpm" (
    echo pnpm dev
) else (
    echo npm run dev
)
echo.
echo 测试 Firecrawl 连接：
echo node scripts\test-firecrawl.js
echo.
echo 项目将在 http://localhost:3000 启动
goto :end

:show_config_reminder
echo.
echo ⚠️  重要提醒：
echo ==================================
echo 1. 请确保本地 Firecrawl 服务在 http://localhost:3002 运行
echo 2. 请在 .env.local 中配置以下必需的 API Keys：
echo    - E2B_API_KEY (必需，用于代码执行沙箱)
echo    - 至少一个 AI 提供商的 API Key：
echo      * ANTHROPIC_API_KEY
echo      * OPENAI_API_KEY
echo      * GEMINI_API_KEY
echo      * GROQ_API_KEY
echo 3. 获取 API Keys 的链接：
echo    - E2B: https://e2b.dev
echo    - Anthropic: https://console.anthropic.com
echo    - OpenAI: https://platform.openai.com
echo    - Google: https://aistudio.google.com/app/apikey
echo    - Groq: https://console.groq.com
echo.
echo 请先启动 Firecrawl 服务，然后运行：
echo node scripts\test-firecrawl.js
echo.
echo 配置完成后，使用以下命令启动项目：
if "!PKG_MANAGER!"=="pnpm" (
    echo pnpm dev
) else (
    echo npm run dev
)

:end
echo.
pause
