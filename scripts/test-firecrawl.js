#!/usr/bin/env node

/**
 * 测试本地 Firecrawl 服务连接
 * 使用方法: node scripts/test-firecrawl.js [url]
 */

import https from 'https';
import http from 'http';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 默认测试 URL
const DEFAULT_TEST_URL = 'https://example.com';

// 从环境变量或默认值获取 Firecrawl 配置
const FIRECRAWL_BASE_URL = process.env.FIRECRAWL_BASE_URL || 'http://localhost:3002';
const FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY || 'dummy_key_for_local';

// 从命令行参数获取测试 URL
const testUrl = process.argv[2] || DEFAULT_TEST_URL;

console.log('🔥 测试 Firecrawl 服务连接');
console.log('='.repeat(50));
console.log(`Firecrawl 服务地址: ${FIRECRAWL_BASE_URL}`);
console.log(`API Key: ${FIRECRAWL_API_KEY.substring(0, 10)}...`);
console.log(`测试 URL: ${testUrl}`);
console.log('='.repeat(50));

async function testFirecrawl() {
  try {
    console.log('\n📡 正在连接 Firecrawl 服务...');

    const requestData = JSON.stringify({
      url: testUrl,
      formats: ['markdown'],
      waitFor: 1000,
      timeout: 10000,
      blockAds: true
    });

    // 确定 API 版本
    const isLocalFirecrawl = FIRECRAWL_BASE_URL.includes('localhost') || FIRECRAWL_BASE_URL.includes('127.0.0.1');
    const apiVersion = isLocalFirecrawl ? 'v0' : 'v1';

    // 直接使用 http 模块，避免代理问题
    const url = new URL(`${FIRECRAWL_BASE_URL}/${apiVersion}/scrape`);
    const isHttps = url.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${FIRECRAWL_API_KEY}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestData)
      },
      // 禁用代理
      agent: false
    };

    const response = await new Promise((resolve, reject) => {
      const req = httpModule.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(15000, () => {
        req.destroy();
        reject(new Error('请求超时'));
      });

      req.write(requestData);
      req.end();
    });

    console.log(`✅ HTTP 状态码: ${response.statusCode}`);

    if (response.statusCode === 200) {
      try {
        const result = JSON.parse(response.data);

        if (result.success && result.data) {
          console.log('🎉 Firecrawl 服务连接成功!');
          console.log(`📄 抓取内容长度: ${result.data.markdown ? result.data.markdown.length : 0} 字符`);
          
          if (result.data.metadata) {
            console.log(`📋 页面标题: ${result.data.metadata.title || '未知'}`);
            console.log(`📝 页面描述: ${result.data.metadata.description || '未知'}`);
          }
          
          // 显示部分内容预览
          if (result.data.markdown) {
            const preview = result.data.markdown.substring(0, 200);
            console.log('\n📖 内容预览:');
            console.log('-'.repeat(30));
            console.log(preview + (result.data.markdown.length > 200 ? '...' : ''));
            console.log('-'.repeat(30));
          }
          
          return true;
        } else {
          console.log('❌ Firecrawl 返回失败结果:');
          console.log(JSON.stringify(result, null, 2));
          return false;
        }
      } catch (parseError) {
        console.log('❌ 解析响应 JSON 失败:');
        console.log('原始响应:', response.data.substring(0, 500));
        return false;
      }
    } else {
      console.log(`❌ HTTP 请求失败，状态码: ${response.statusCode}`);
      console.log('响应内容:', response.data);
      return false;
    }
    
  } catch (error) {
    console.log('❌ 连接 Firecrawl 服务失败:');
    console.log(`错误类型: ${error.name}`);
    console.log(`错误信息: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 建议检查:');
      console.log('1. Firecrawl 服务是否在运行');
      console.log('2. 服务地址是否正确 (当前: ' + FIRECRAWL_BASE_URL + ')');
      console.log('3. 端口是否被占用或防火墙阻止');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n💡 建议检查:');
      console.log('1. 域名或 IP 地址是否正确');
      console.log('2. 网络连接是否正常');
    }
    
    return false;
  }
}

async function testHealthCheck() {
  try {
    console.log('\n🏥 检查 Firecrawl 服务健康状态...');

    const response = await fetch(`${FIRECRAWL_BASE_URL}/health`, {
      method: 'GET',
      timeout: 5000
    });

    if (response.ok) {
      console.log('✅ Firecrawl 服务健康检查通过');
      return true;
    } else {
      console.log(`⚠️  健康检查返回状态码: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`⚠️  健康检查失败: ${error.message}`);
    return false;
  }
}

// 主函数
async function main() {
  console.log('开始测试...\n');

  // 测试抓取功能
  const scrapeOk = await testFirecrawl();

  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果汇总:');
  console.log(`抓取功能: ${scrapeOk ? '✅ 通过' : '❌ 失败'}`);

  if (scrapeOk) {
    console.log('\n🎉 测试通过! Firecrawl 服务配置正确。');
    console.log('现在可以启动 Open Lovable 项目了:');
    console.log('npm run dev');
    process.exit(0);
  } else {
    console.log('\n❌ 测试失败，请检查 Firecrawl 服务配置。');
    console.log('\n💡 确认事项:');
    console.log('1. Firecrawl 服务是否在 http://localhost:3002 运行');
    console.log('2. 服务是否支持 /v1/scrape 端点');
    console.log('3. 网络连接是否正常');
    process.exit(1);
  }
}

// 运行测试
main().catch(error => {
  console.error('测试过程中发生未预期的错误:', error);
  process.exit(1);
});
