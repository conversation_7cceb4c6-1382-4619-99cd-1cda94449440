#!/bin/bash

# Open Lovable 快速设置脚本
# 用于配置本地 Firecrawl 环境

set -e

echo "🚀 Open Lovable 快速设置脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 Node.js
check_node() {
    echo -e "${BLUE}检查 Node.js...${NC}"
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js 18+${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo -e "${RED}❌ Node.js 版本过低 (当前: $(node -v))，需要 18+${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Node.js 版本: $(node -v)${NC}"
}

# 检查包管理器
check_package_manager() {
    echo -e "${BLUE}检查包管理器...${NC}"
    if command -v pnpm &> /dev/null; then
        PKG_MANAGER="pnpm"
        echo -e "${GREEN}✅ 使用 pnpm${NC}"
    elif command -v npm &> /dev/null; then
        PKG_MANAGER="npm"
        echo -e "${GREEN}✅ 使用 npm${NC}"
    else
        echo -e "${RED}❌ 未找到 npm 或 pnpm${NC}"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}安装项目依赖...${NC}"
    if [ "$PKG_MANAGER" = "pnpm" ]; then
        pnpm install
    else
        npm install
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 创建环境变量文件
create_env_file() {
    echo -e "${BLUE}配置环境变量...${NC}"
    
    if [ ! -f ".env.local" ]; then
        cp .env.example .env.local
        echo -e "${GREEN}✅ 已创建 .env.local 文件${NC}"
    else
        echo -e "${YELLOW}⚠️  .env.local 文件已存在，跳过创建${NC}"
    fi
    
    # 检查是否已配置本地 Firecrawl
    if grep -q "FIRECRAWL_BASE_URL=http://localhost:3002" .env.local; then
        echo -e "${GREEN}✅ 本地 Firecrawl 配置已存在${NC}"
    else
        echo -e "${YELLOW}📝 配置本地 Firecrawl...${NC}"
        
        # 添加或更新 FIRECRAWL_BASE_URL
        if grep -q "FIRECRAWL_BASE_URL=" .env.local; then
            sed -i.bak 's|FIRECRAWL_BASE_URL=.*|FIRECRAWL_BASE_URL=http://localhost:3002|' .env.local
        else
            echo "FIRECRAWL_BASE_URL=http://localhost:3002" >> .env.local
        fi
        
        # 设置虚拟 API Key
        if grep -q "FIRECRAWL_API_KEY=your_firecrawl_api_key_here" .env.local; then
            sed -i.bak 's|FIRECRAWL_API_KEY=your_firecrawl_api_key_here|FIRECRAWL_API_KEY=dummy_key_for_local|' .env.local
        fi
        
        echo -e "${GREEN}✅ 本地 Firecrawl 配置完成${NC}"
    fi
}

# 测试 Firecrawl 连接
test_firecrawl() {
    echo -e "${BLUE}测试 Firecrawl 连接...${NC}"
    
    # 检查脚本是否存在
    if [ ! -f "scripts/test-firecrawl.js" ]; then
        echo -e "${RED}❌ 测试脚本不存在${NC}"
        return 1
    fi
    
    # 运行测试
    if node scripts/test-firecrawl.js; then
        echo -e "${GREEN}✅ Firecrawl 连接测试通过${NC}"
        return 0
    else
        echo -e "${RED}❌ Firecrawl 连接测试失败${NC}"
        return 1
    fi
}

# 显示配置提醒
show_config_reminder() {
    echo -e "${YELLOW}"
    echo "⚠️  重要提醒："
    echo "=================================="
    echo "1. 请确保本地 Firecrawl 服务在 http://localhost:3002 运行"
    echo "2. 请在 .env.local 中配置以下必需的 API Keys："
    echo "   - E2B_API_KEY (必需，用于代码执行沙箱)"
    echo "   - 至少一个 AI 提供商的 API Key："
    echo "     * ANTHROPIC_API_KEY"
    echo "     * OPENAI_API_KEY" 
    echo "     * GEMINI_API_KEY"
    echo "     * GROQ_API_KEY"
    echo "3. 获取 API Keys 的链接："
    echo "   - E2B: https://e2b.dev"
    echo "   - Anthropic: https://console.anthropic.com"
    echo "   - OpenAI: https://platform.openai.com"
    echo "   - Google: https://aistudio.google.com/app/apikey"
    echo "   - Groq: https://console.groq.com"
    echo -e "${NC}"
}

# 显示启动命令
show_start_commands() {
    echo -e "${GREEN}"
    echo "🎉 设置完成！"
    echo "=================================="
    echo "启动开发服务器："
    if [ "$PKG_MANAGER" = "pnpm" ]; then
        echo "pnpm dev"
    else
        echo "npm run dev"
    fi
    echo ""
    echo "测试 Firecrawl 连接："
    echo "node scripts/test-firecrawl.js"
    echo ""
    echo "项目将在 http://localhost:3000 启动"
    echo -e "${NC}"
}

# 主函数
main() {
    echo "开始设置..."
    echo ""
    
    check_node
    check_package_manager
    install_dependencies
    create_env_file
    
    echo ""
    echo -e "${BLUE}正在测试 Firecrawl 连接...${NC}"
    
    if test_firecrawl; then
        echo ""
        show_start_commands
    else
        echo ""
        show_config_reminder
        echo -e "${YELLOW}请先启动 Firecrawl 服务，然后运行：${NC}"
        echo -e "${YELLOW}node scripts/test-firecrawl.js${NC}"
        echo ""
        echo -e "${BLUE}配置完成后，使用以下命令启动项目：${NC}"
        if [ "$PKG_MANAGER" = "pnpm" ]; then
            echo "pnpm dev"
        else
            echo "npm run dev"
        fi
    fi
}

# 运行主函数
main
