# SANDBOX CONFIGURATION
# Choose between E2B cloud sandbox or local sandbox
SANDBOX_TYPE=local  # Options: 'e2b' or 'local'

# E2B Cloud Sandbox (if SANDBOX_TYPE=e2b)
# Get yours at https://e2b.dev
E2B_API_KEY=your_e2b_api_key_here

# Local Sandbox Configuration (if SANDBOX_TYPE=local)
LOCAL_SANDBOX_PORT=5173
LOCAL_SANDBOX_DIR=./sandbox

# REQUIRED - Web scraping for cloning websites
# For local Firecrawl: set FIRECRAWL_BASE_URL and use any dummy API key
# For cloud Firecrawl: get yours at https://firecrawl.dev
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
FIRECRAWL_BASE_URL=http://localhost:3002

# Optional: Override API path (default: v2/scrape for local, v1/scrape for cloud)
# FIRECRAWL_API_PATH=v2/scrape

# OPTIONAL - AI Providers (need at least one)
# Get yours at https://console.anthropic.com
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Get yours at https://platform.openai.com
OPENAI_API_KEY=your_openai_api_key_here

# Get yours at https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Get yours at https://console.groq.com
GROQ_API_KEY=your_groq_api_key_here