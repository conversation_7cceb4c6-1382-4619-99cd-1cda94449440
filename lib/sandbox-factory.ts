/**
 * Sandbox Factory
 * Creates appropriate sandbox instance based on configuration
 */

import { Sandbox as E2BSandbox } from '@e2b/code-interpreter';
import { LocalSandbox } from './local-sandbox';

export type SandboxType = 'e2b' | 'local';

export interface SandboxInterface {
  runCode(code: string): Promise<any>;
  files: {
    write(path: string, content: string): Promise<void>;
    read(path: string): Promise<string>;
    delete(path: string): Promise<void>;
  };
  commands: {
    run(command: string, options?: any): Promise<any>;
  };
  getHost(port?: number): string;
  kill(): Promise<void>;
  setTimeout?(ms: number): void;
}

export class SandboxFactory {
  static getSandboxType(): SandboxType {
    const type = process.env.SANDBOX_TYPE?.toLowerCase();
    return (type === 'local' || type === 'e2b') ? type : 'local';
  }

  static async create(options?: { timeoutMs?: number }): Promise<SandboxInterface> {
    const sandboxType = this.getSandboxType();
    
    console.log(`[SandboxFactory] Creating ${sandboxType} sandbox...`);
    
    if (sandboxType === 'e2b') {
      if (!process.env.E2B_API_KEY) {
        throw new Error('E2B_API_KEY is required when SANDBOX_TYPE=e2b');
      }
      
      const sandbox = await E2BSandbox.create({
        apiKey: process.env.E2B_API_KEY,
        timeoutMs: options?.timeoutMs
      });
      
      return this.wrapE2BSandbox(sandbox);
    } else {
      return await LocalSandbox.create(options);
    }
  }

  static async connect(sandboxId: string, options?: { apiKey?: string }): Promise<SandboxInterface> {
    const sandboxType = this.getSandboxType();
    
    console.log(`[SandboxFactory] Connecting to ${sandboxType} sandbox: ${sandboxId}`);
    
    if (sandboxType === 'e2b') {
      if (!process.env.E2B_API_KEY) {
        throw new Error('E2B_API_KEY is required when SANDBOX_TYPE=e2b');
      }
      
      const sandbox = await E2BSandbox.connect(sandboxId, {
        apiKey: options?.apiKey || process.env.E2B_API_KEY
      });
      
      return this.wrapE2BSandbox(sandbox);
    } else {
      return await LocalSandbox.connect(sandboxId, options);
    }
  }

  private static wrapE2BSandbox(sandbox: any): SandboxInterface {
    return {
      runCode: (code: string) => sandbox.runCode(code),
      files: {
        write: (path: string, content: string) => sandbox.files.write(path, content),
        read: (path: string) => sandbox.files.read(path),
        delete: (path: string) => sandbox.files.delete(path)
      },
      commands: {
        run: (command: string, options?: any) => sandbox.commands.run(command, options)
      },
      getHost: (port?: number) => sandbox.getHost(port),
      kill: () => sandbox.kill(),
      setTimeout: (ms: number) => sandbox.setTimeout?.(ms)
    };
  }

  static getPreviewUrl(sandbox: SandboxInterface, port: number = 5173): string {
    const sandboxType = this.getSandboxType();
    const host = sandbox.getHost(port);
    
    if (sandboxType === 'local') {
      return `http://${host}`;
    } else {
      return `https://${host}`;
    }
  }

  static isLocal(): boolean {
    return this.getSandboxType() === 'local';
  }

  static isE2B(): boolean {
    return this.getSandboxType() === 'e2b';
  }
}
