# Open Lovable 快速启动指南

## 🚀 一键设置

### 自动设置 (推荐)

**macOS/Linux:**
```bash
git clone https://github.com/mendableai/open-lovable.git
cd open-lovable
npm run setup
```

**Windows:**
```bash
git clone https://github.com/mendableai/open-lovable.git
cd open-lovable
npm run setup:win
```

### 手动设置

1. **克隆项目**
```bash
git clone https://github.com/mendableai/open-lovable.git
cd open-lovable
npm install
```

2. **配置环境变量**
```bash
cp .env.example .env.local
```

编辑 `.env.local`:
```env
# 必需 - E2B 沙箱服务
E2B_API_KEY=your_e2b_api_key_here

# 本地 Firecrawl 配置
FIRECRAWL_BASE_URL=http://localhost:3002
FIRECRAWL_API_KEY=dummy_key_for_local

# 至少配置一个 AI 提供商
ANTHROPIC_API_KEY=your_anthropic_api_key_here
# OPENAI_API_KEY=your_openai_api_key_here
# GEMINI_API_KEY=your_gemini_api_key_here
# GROQ_API_KEY=your_groq_api_key_here
```

3. **测试 Firecrawl 连接**
```bash
npm run test:firecrawl
```

4. **启动项目**
```bash
npm run dev
```

## 📋 必需的 API Keys

### E2B (必需)
- 用途: 代码执行沙箱
- 获取: https://e2b.dev
- 免费额度: 有

### AI 提供商 (至少一个)

**Anthropic Claude (推荐)**
- 获取: https://console.anthropic.com
- 模型: Claude Sonnet 4

**OpenAI**
- 获取: https://platform.openai.com
- 模型: GPT-5

**Google Gemini**
- 获取: https://aistudio.google.com/app/apikey
- 模型: Gemini 2.5 Pro

**Groq (快速推理)**
- 获取: https://console.groq.com
- 模型: Kimi K2 Instruct (推荐)

## 🔧 本地 Firecrawl 配置

### 重要说明
- ✅ 项目已配置支持本地 Firecrawl (v0 API)
- ✅ 自动检测本地/云端服务并使用正确的 API 版本
- ✅ 本地服务使用 `/v0/scrape`，云端服务使用 `/v1/scrape`

### 验证本地 Firecrawl
```bash
# 方法 1: 使用项目测试脚本
npm run test:firecrawl

# 方法 2: 直接使用 curl
curl -X POST http://localhost:3002/v0/scrape \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'
```

### 代理配置
如果系统使用 HTTP 代理，可能需要配置例外：
```bash
export NO_PROXY=localhost,127.0.0.1
```

## 🎯 使用方法

1. **启动项目**
```bash
npm run dev
```

2. **打开浏览器**
访问 http://localhost:3000

3. **开始使用**
- 在聊天界面描述你想要的应用
- 或者输入网址让 AI 克隆网站
- 实时查看生成的代码和预览

## 🛠️ 故障排除

### Firecrawl 连接失败
```bash
# 检查服务状态
curl http://localhost:3002/

# 测试抓取功能
npm run test:firecrawl
```

### E2B 沙箱失败
- 检查 E2B_API_KEY 是否正确
- 确认账户有足够配额

### AI 响应失败
- 确保至少配置了一个 AI 提供商的 API Key
- 检查 API Key 是否有效

## 📁 项目结构

```
open-lovable/
├── app/                    # Next.js 应用
│   ├── api/               # API 路由
│   └── page.tsx           # 主页面
├── scripts/               # 工具脚本
│   ├── setup.sh          # 自动设置脚本
│   └── test-firecrawl-curl.sh  # Firecrawl 测试
├── .env.example          # 环境变量模板
└── LOCAL_SETUP_GUIDE.md  # 详细配置指南
```

## 🔗 相关链接

- [详细配置指南](./LOCAL_SETUP_GUIDE.md)
- [原项目 GitHub](https://github.com/mendableai/open-lovable)
- [Lovable.dev](https://lovable.dev/) - 云端版本

## 📞 支持

如果遇到问题：
1. 查看 [LOCAL_SETUP_GUIDE.md](./LOCAL_SETUP_GUIDE.md) 详细文档
2. 运行 `npm run test:firecrawl` 测试 Firecrawl 连接
3. 检查浏览器控制台和终端日志
4. 确认所有必需的 API Keys 已正确配置

---

**🎉 配置完成后，你就可以开始使用 AI 构建 React 应用了！**
