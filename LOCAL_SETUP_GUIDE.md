# Open Lovable 本地配置指南

本指南将帮助你在本地运行 Open Lovable 项目，并配置使用本地的 Firecrawl 服务而不是云端服务。

## 项目简介

Open Lovable 是一个基于 AI 的 React 应用构建工具，可以通过聊天界面快速创建 React 应用。它使用 Firecrawl 进行网页抓取，E2B 提供代码执行沙箱环境。

## 前置要求

- Node.js 18+ 
- npm 或 pnpm
- 本地运行的 Firecrawl 服务 (http://localhost:3002)
- E2B API Key (用于代码执行沙箱)
- 至少一个 AI 提供商的 API Key

## 安装步骤

### 1. 克隆项目

```bash
git clone https://github.com/mendableai/open-lovable.git
cd open-lovable
```

### 2. 安装依赖

```bash
npm install
# 或者使用 pnpm
pnpm install
```

### 3. 环境变量配置

创建 `.env.local` 文件：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置以下环境变量：

```env
# 必需 - E2B 沙箱服务 (用于代码执行)
E2B_API_KEY=your_e2b_api_key_here

# 必需 - Firecrawl 配置 (用于网页抓取)
# 使用本地 Firecrawl 实例
FIRECRAWL_BASE_URL=http://localhost:3002
FIRECRAWL_API_KEY=dummy_key_for_local

# 可选 - AI 提供商 (至少需要一个)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
GROQ_API_KEY=your_groq_api_key_here
```

### 4. 获取必需的 API Keys

#### E2B API Key (必需)
1. 访问 [https://e2b.dev](https://e2b.dev)
2. 注册账户并获取 API Key
3. 将 API Key 添加到 `.env.local` 文件

#### AI 提供商 API Keys (至少需要一个)

**Anthropic Claude:**
- 访问 [https://console.anthropic.com](https://console.anthropic.com)
- 获取 API Key

**OpenAI:**
- 访问 [https://platform.openai.com](https://platform.openai.com)
- 获取 API Key

**Google Gemini:**
- 访问 [https://aistudio.google.com/app/apikey](https://aistudio.google.com/app/apikey)
- 获取 API Key

**Groq:**
- 访问 [https://console.groq.com](https://console.groq.com)
- 获取 API Key (推荐使用 Kimi K2 模型)

### 5. 本地 Firecrawl 配置

确保你的本地 Firecrawl 服务正在运行：

```bash
# 检查 Firecrawl 服务状态 (注意：本地版本默认使用 v2 API)
curl http://localhost:3002/v2/scrape -X POST \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com", "formats": ["markdown"]}'
```

**重要说明：**
- 本地 Firecrawl 服务默认使用 `/v2/scrape` 端点 (推荐)
- 云端 Firecrawl 服务使用 `/v1/scrape` 端点
- 项目已自动配置根据服务地址选择正确的 API 版本
- 可通过 `FIRECRAWL_API_PATH` 环境变量自定义 API 路径

如果 Firecrawl 服务未运行，请参考 Firecrawl 的官方文档进行本地部署。

**代理配置注意事项：**
如果你的系统使用 HTTP 代理，确保 localhost 请求能够正常工作。可能需要配置代理例外：
```bash
export NO_PROXY=localhost,127.0.0.1
```

## 运行项目

### 开发模式

```bash
npm run dev
# 或者
pnpm dev
```

项目将在 [http://localhost:3000](http://localhost:3000) 启动。

### 生产模式

```bash
# 构建项目
npm run build

# 启动生产服务器
npm start
```

## 功能说明

### 主要功能

1. **AI 聊天界面**: 通过自然语言描述来创建 React 应用
2. **网页克隆**: 输入网址，AI 会抓取内容并创建相似的应用
3. **实时预览**: 在沙箱环境中实时预览生成的应用
4. **代码编辑**: 可以查看和编辑生成的代码
5. **包管理**: 自动检测和安装所需的 npm 包

### 支持的 AI 模型

- GPT-5 (OpenAI)
- Kimi K2 Instruct (Moonshot AI) - 推荐
- Claude Sonnet 4 (Anthropic)
- Gemini 2.5 Pro (Google)

## 使用指南

### 基本使用

1. 打开 [http://localhost:3000](http://localhost:3000)
2. 在聊天界面中描述你想要创建的应用
3. 等待 AI 生成代码并在沙箱中运行
4. 查看预览并进行进一步的修改

### 网页克隆功能

1. 在输入框中输入要克隆的网址
2. 点击发送，AI 会抓取网页内容
3. AI 会分析网页结构并生成相似的 React 应用
4. 在右侧预览窗口查看结果

### 代码查看和编辑

1. 点击界面中的代码块可以查看完整代码
2. 可以要求 AI 修改特定的功能或样式
3. 所有更改会实时反映在预览窗口中

## 故障排除

### 常见问题

**1. Firecrawl 连接失败**
- 确保本地 Firecrawl 服务在 http://localhost:3002 运行
- 检查 `FIRECRAWL_BASE_URL` 环境变量是否正确设置

**2. E2B 沙箱创建失败**
- 检查 `E2B_API_KEY` 是否正确设置
- 确保 E2B 账户有足够的配额

**3. AI 响应失败**
- 确保至少配置了一个 AI 提供商的 API Key
- 检查 API Key 是否有效且有足够的配额

**4. 包安装失败**
- 检查网络连接
- 尝试重启沙箱环境

### 日志查看

开发模式下，可以在浏览器控制台和终端中查看详细日志：

```bash
# 查看服务器日志
npm run dev

# 在浏览器中按 F12 打开开发者工具查看客户端日志
```

## 配置选项

### 应用配置

主要配置文件位于 `config/app.config.ts`，包含：

- E2B 沙箱超时设置
- AI 模型配置
- UI 配置选项
- 包管理配置

### 环境变量说明

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `E2B_API_KEY` | 是 | E2B 沙箱服务 API Key |
| `FIRECRAWL_BASE_URL` | 否 | Firecrawl 服务地址，默认为云端服务 |
| `FIRECRAWL_API_KEY` | 是 | Firecrawl API Key，本地服务可使用任意值 |
| `FIRECRAWL_API_PATH` | 否 | 自定义 API 路径，默认本地用 v2/scrape，云端用 v1/scrape |
| `ANTHROPIC_API_KEY` | 否 | Anthropic Claude API Key |
| `OPENAI_API_KEY` | 否 | OpenAI API Key |
| `GEMINI_API_KEY` | 否 | Google Gemini API Key |
| `GROQ_API_KEY` | 否 | Groq API Key |

## 测试

### Firecrawl 连接测试

```bash
# 测试 Firecrawl 连接
npm run test:firecrawl

# 或者直接运行脚本
./scripts/test-firecrawl-curl.sh https://example.com
```

### 其他测试

项目包含多个测试脚本：

```bash
# 运行所有测试
npm run test:all

# 单独运行测试
npm run test:integration  # E2B 集成测试
npm run test:api         # API 端点测试
npm run test:code        # 代码执行测试
```

## 开发说明

### 项目结构

```
open-lovable/
├── app/                 # Next.js 应用目录
│   ├── api/            # API 路由
│   ├── components/     # React 组件
│   └── page.tsx        # 主页面
├── components/         # 共享组件
├── config/            # 配置文件
├── lib/               # 工具函数
├── types/             # TypeScript 类型定义
└── public/            # 静态资源
```

### 主要 API 端点

- `/api/scrape-url-enhanced` - 网页抓取 (使用 Firecrawl)
- `/api/scrape-screenshot` - 网页截图
- `/api/create-ai-sandbox` - 创建 E2B 沙箱
- `/api/generate-ai-code-stream` - AI 代码生成
- `/api/apply-ai-code` - 应用生成的代码

## 许可证

MIT License

## 支持

如果遇到问题，可以：

1. 查看项目的 GitHub Issues
2. 参考官方文档
3. 检查环境变量配置
4. 查看服务器和浏览器日志

---

**注意**: 本地 Firecrawl 配置已经完成，确保你的 Firecrawl 服务在 `http://localhost:3002` 运行，并在 `.env.local` 中正确配置 `FIRECRAWL_BASE_URL`。
