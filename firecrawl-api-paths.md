# 🔥 Firecrawl API 完整路径指南

## 📋 概述

本文档详细列出了Firecrawl服务的所有API端点路径，基于项目源码的最新分析。

---

## 🔗 API基础信息

### 服务地址
- **本地开发**: `http://localhost:3002`
- **生产环境**: 根据部署配置确定

### API版本
- **v0**: 早期版本 (兼容性支持)
- **v1**: 主要版本 (广泛使用)
- **v2**: 最新版本 (推荐使用)

### 认证方式
- 自托管版本: 可选API Key认证
- 云版本: 必需API Key认证

---

## 📊 核心功能API

### 1. 网页抓取 (Scrape)

**功能**: 抓取单个网页并提取内容

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/scrape` | v2 | 推荐版本，支持最新功能 |
| POST | `/v1/scrape` | v1 | 主要版本，功能完整 |
| POST | `/v0/v0/scrape` | v0 | 早期版本，基本功能 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/scrape \
  -H 'Content-Type: application/json' \
  -d '{
    "url": "https://example.com",
    "formats": ["markdown", "html"]
  }'
```

---

### 2. 网站爬取 (Crawl)

**功能**: 爬取整个网站的所有页面

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/crawl` | v2 | 推荐版本，支持参数预览 |
| POST | `/v1/crawl` | v1 | 主要版本，功能稳定 |
| POST | `/v0/v0/crawl` | v0 | 早期版本，基本功能 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/crawl \
  -H 'Content-Type: application/json' \
  -d '{
    "url": "https://example.com",
    "limit": 100,
    "scrapeOptions": {
      "formats": ["markdown"]
    }
  }'
```

---

### 3. 批量抓取 (Batch Scrape)

**功能**: 同时抓取多个URL

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/batch/scrape` | v2 | 推荐版本 |
| POST | `/v1/batch/scrape` | v1 | 主要版本 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/batch/scrape \
  -H 'Content-Type: application/json' \
  -d '{
    "urls": ["https://example1.com", "https://example2.com"],
    "formats": ["markdown"]
  }'
```

---

### 4. 网站地图 (Map)

**功能**: 生成网站结构图，获取所有可访问链接

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/map` | v2 | 推荐版本 |
| POST | `/v1/map` | v1 | 主要版本 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/map \
  -H 'Content-Type: application/json' \
  -d '{
    "url": "https://example.com"
  }'
```

---

### 5. 网络搜索 (Search)

**功能**: 搜索网页并获取完整内容

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/search` | v2 | 推荐版本 |
| POST | `/v1/search` | v1 | 主要版本 |
| POST | `/v1/x402/search` | v1 | 付费搜索 (X402协议) |
| POST | `/v0/v0/search` | v0 | 早期版本 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/search \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "web scraping tools",
    "limit": 5
  }'
```

---

### 6. AI数据提取 (Extract)

**功能**: 使用AI从网页或网站中提取结构化数据

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/extract` | v2 | 推荐版本 |
| POST | `/v1/extract` | v1 | 主要版本 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/extract \
  -H 'Content-Type: application/json' \
  -d '{
    "urls": ["https://example.com"],
    "prompt": "提取页面的关键信息",
    "schema": {
      "type": "object",
      "properties": {
        "title": {"type": "string"},
        "description": {"type": "string"}
      }
    }
  }'
```

---

## 📈 任务状态查询

### 爬取任务状态

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/crawl/:jobId` | v2 | 查询爬取任务状态 |
| GET | `/v1/crawl/:jobId` | v1 | 查询爬取任务状态 |
| GET | `/v0/v0/crawl/status/:jobId` | v0 | 查询爬取任务状态 |

**使用示例**:
```bash
curl http://localhost:3002/v2/crawl/job-123-456-789
```

---

### 抓取任务状态

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/scrape/:jobId` | v2 | 查询抓取任务状态 |
| GET | `/v1/scrape/:jobId` | v1 | 查询抓取任务状态 |

---

### 批量抓取任务状态

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/batch/scrape/:jobId` | v2 | 查询批量抓取任务状态 |
| GET | `/v1/batch/scrape/:jobId` | v1 | 查询批量抓取任务状态 |

---

### 提取任务状态

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/extract/:jobId` | v2 | 查询提取任务状态 |
| GET | `/v1/extract/:jobId` | v1 | 查询提取任务状态 |

---

### 进行中的任务

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/crawl/ongoing` | v2 | 获取正在进行的爬取任务 |
| GET | `/v2/crawl/active` | v2 | 获取活跃的爬取任务 |
| GET | `/v1/crawl/ongoing` | v1 | 获取正在进行的爬取任务 |
| GET | `/v1/crawl/active` | v1 | 获取活跃的爬取任务 |

---

## ⚙️ 高级功能

### 爬取参数预览

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v2/crawl/params-preview` | v2 | 预览爬取参数配置 |

**使用示例**:
```bash
curl -X POST http://localhost:3002/v2/crawl/params-preview \
  -H 'Content-Type: application/json' \
  -d '{
    "url": "https://example.com",
    "limit": 50
  }'
```

---

### LLM.txt 生成

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v1/llmstxt` | v1 | 生成LLM可读的文本格式 |
| GET | `/v1/llmstxt/:jobId` | v1 | 查询LLM.txt生成状态 |

---

### 深度研究 (Deep Research)

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| POST | `/v1/deep-research` | v1 | 执行深度研究分析 |
| GET | `/v1/deep-research/:jobId` | v1 | 查询深度研究状态 |

---

## 🛠️ 任务管理

### 任务取消

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| DELETE | `/v2/crawl/:jobId` | v2 | 取消爬取任务 |
| DELETE | `/v1/crawl/:jobId` | v1 | 取消爬取任务 |
| DELETE | `/v2/batch/scrape/:jobId` | v2 | 取消批量抓取任务 |
| DELETE | `/v1/batch/scrape/:jobId` | v1 | 取消批量抓取任务 |
| DELETE | `/v0/v0/crawl/cancel/:jobId` | v0 | 取消爬取任务 |

---

### 错误查询

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/crawl/:jobId/errors` | v2 | 获取爬取任务错误信息 |
| GET | `/v1/crawl/:jobId/errors` | v1 | 获取爬取任务错误信息 |
| GET | `/v2/batch/scrape/:jobId/errors` | v2 | 获取批量抓取错误信息 |
| GET | `/v1/batch/scrape/:jobId/errors` | v1 | 获取批量抓取错误信息 |

---

### 并发检查

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/concurrency-check` | v2 | 检查系统并发状态 |
| GET | `/v1/concurrency-check` | v1 | 检查系统并发状态 |

---

## 📊 监控和统计

### 团队使用统计

| 方法 | 路径 | 版本 | 说明 |
|------|------|------|------|
| GET | `/v2/team/credit-usage` | v2 | 获取团队积分使用情况 |
| GET | `/v1/team/credit-usage` | v1 | 获取团队积分使用情况 |
| GET | `/v2/team/token-usage` | v2 | 获取团队Token使用情况 |
| GET | `/v1/team/token-usage` | v1 | 获取团队Token使用情况 |

---

## 🩺 健康检查和系统

### 基础检查

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/` | 根路径响应 |
| GET | `/test` | 简单测试端点 |

### 服务健康检查

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/serverHealthCheck` | 服务器健康状态检查 |
| GET | `/serverHealthCheck/notify` | 带通知的健康检查 |

### 管理面板

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/admin/:authKey/queues` | Bull队列管理面板 |

### 域名统计

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/domain-frequency` | 域名访问频率统计 |

---

## 🔌 实时通信

### WebSocket 连接

| 协议 | 路径 | 版本 | 说明 |
|------|------|------|------|
| WS | `/v1/crawl/:jobId` | v1 | 爬取任务实时状态 |
| WS | `/v2/crawl/:jobId` | v2 | 爬取任务实时状态 |

**使用示例**:
```javascript
// JavaScript WebSocket 连接示例
const ws = new WebSocket('ws://localhost:3002/v2/crawl/job-123-456-789');
ws.onmessage = (event) => {
  console.log('任务状态更新:', event.data);
};
```

---

## 📋 版本对比

### V2 vs V1 vs V0

| 功能 | V0 | V1 | V2 |
|------|----|----|----|
| 网页抓取 | ✅ | ✅ | ✅ (推荐) |
| 网站爬取 | ✅ | ✅ | ✅ (推荐) |
| 批量操作 | ❌ | ✅ | ✅ (推荐) |
| 网站地图 | ❌ | ✅ | ✅ (推荐) |
| 网络搜索 | ✅ | ✅ | ✅ (推荐) |
| AI提取 | ❌ | ✅ | ✅ (推荐) |
| 参数预览 | ❌ | ❌ | ✅ (独有) |
| 实时状态 | ❌ | ✅ | ✅ (改进) |
| 错误处理 | 基本 | 良好 | 优秀 |

### 推荐使用策略

1. **新项目**: 优先使用 V2 API
2. **现有项目**: V1 API 保持兼容
3. **简单需求**: V0 API 基本功能

---

## 🔧 快速开始

### 1. 启动服务
```bash
cd /Users/<USER>/firecrawl/apps/api
pnpm run start
```

### 2. 测试API
```bash
# 健康检查
curl http://localhost:3002/test

# 抓取页面
curl -X POST http://localhost:3002/v2/scrape \
  -H 'Content-Type: application/json' \
  -d '{"url": "https://example.com"}'
```

### 3. 查看队列状态
```
访问: http://localhost:3002/admin/CHANGEME/queues
```

---

## ⚠️ 注意事项

1. **认证**: 自托管版本API Key是可选的
2. **并发**: 注意系统的并发限制
3. **资源**: 爬取大量页面需要足够内存
4. **频率**: 遵守robots.txt和网站政策
5. **成本**: AI功能需要配置API Key

---

## 📚 相关文档

- [Firecrawl 官方文档](https://docs.firecrawl.dev)
- [API 参考](https://docs.firecrawl.dev/api-reference)
- [SDK 文档](https://docs.firecrawl.dev/sdks)

---

*最后更新: 2025年1月*
*基于 Firecrawl 源码分析*
